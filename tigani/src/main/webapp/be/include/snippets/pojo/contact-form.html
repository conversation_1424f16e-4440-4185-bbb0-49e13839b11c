<!-- Contact Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_CONTACT_SAVE', '{{ routes("BE_CONTACT_SAVE") }}');
    addRoute('BE_CONTACT_OPERATE', '{{ routes("BE_CONTACT_OPERATE") }}');
    addRoute('BE_DATA_COUNTRIES', '{{ routes("BE_DATA_COUNTRIES") }}');
    addRoute('BE_DATA_CITIES', '{{ routes("BE_DATA_CITIES") }}');
    addRoute('BE_DATA_PROVINCES', '{{ routes("BE_DATA_PROVINCES") }}');
    addRoute('BE_DATA_ENTITY', '{{ routes("BE_DATA_ENTITY") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_CONTACT_SAVE') %}
            {% if curContact.id is not empty %}
            {% set postUrl = routes('BE_CONTACT_SAVE') + '?contactId=' + curContact.id %}
            {% endif %}

            <form id="contact-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery">
                <!-- Contact Type Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Tipo Contatto: <span class="text-red-500">*</span>
                    </label>
                    <select id="contactType" name="contactType" data-hs-select='{
                        "hasSearch": false,
                        "placeholder": "Seleziona tipo contatto...",
                        "toggleTag": "<button type=\"button\"></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                    }' required {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                    <option value="">Seleziona tipo contatto</option>
                    <option value="PERSON" {% if curContact is not empty and curContact.contactType is not empty and curContact.contactType equals 'PERSON' %}selected{% endif %}>Persona</option>
                    <option value="COMPANY" {% if curContact is not empty and curContact.contactType is not empty and curContact.contactType equals 'COMPANY' %}selected{% endif %}>Azienda</option>
                    </select>
                </div>

                <!-- Person Fields (shown when contactType is PERSON) -->
                <div id="person-fields" class="mb-4 hidden">
                    <!-- First Name -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Nome: <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="firstName" name="firstName" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il nome" value="{% if curContact is not empty %}{{ curContact.firstName }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>

                    <!-- Last Name -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Cognome: <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="lastName" name="lastName" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il cognome" value="{% if curContact is not empty %}{{ curContact.lastName }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>

                    <!-- Birth Country -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Paese di Nascita:
                        </label>
                        <select id="birthCountryCode" name="birthCountryCode" data-hs-select='{
                            "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_COUNTRIES") }}",
                            "apiQuery": "selected={{ curContact.birthCountryCode }}&q",
                            "apiSearchMinLength": 1,
                            "apiDataPart": "results",
                            "apiFieldsMap": {
                                "id": "id",
                                "val": "id",
                                "title": "text"
                            },
                            "apiSelectedValues": "{{ curContact.birthCountryCode }}",
                            "hasSearch": true,
                            "apiLoadMore": true,
                            "placeholder": "Cerca paese...",
                            "toggleTag": "<button type=\"button\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                        }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona paese</option>
                        </select>
                    </div>

                    <!-- Birth Province -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Provincia di Nascita:
                        </label>
                        <select id="birthProvinceCode" name="birthProvinceCode" data-hs-select='{
                            "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_PROVINCES") }}",
                            "apiQuery": "selected={{ curContact.birthProvinceCode }}&q",
                            "apiSearchMinLength": 1,
                            "apiDataPart": "results",
                            "apiFieldsMap": {
                                "id": "id",
                                "val": "id",
                                "title": "text"
                            },
                            "apiSelectedValues": "{{ curContact.birthProvinceCode }}",
                            "hasSearch": true,
                            "apiLoadMore": true,
                            "placeholder": "Cerca provincia...",
                            "toggleTag": "<button type=\"button\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                        }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona provincia</option>
                        </select>
                    </div>

                    <!-- Birth City -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Città di Nascita:
                        </label>
                        <select id="birthCity" name="birthCity" data-hs-select='{
                            "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_CITIES") }}",
                            "apiQuery": "selected={{ curContact.birthCity }}&q",
                            "apiSearchMinLength": 1,
                            "apiDataPart": "results",
                            "apiFieldsMap": {
                                "id": "id",
                                "val": "id",
                                "title": "text"
                            },
                            "apiSelectedValues": "{{ curContact.birthCity }}",
                            "hasSearch": true,
                            "apiLoadMore": true,
                            "placeholder": "Cerca città...",
                            "toggleTag": "<button type=\"button\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                        }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona città</option>
                        </select>
                    </div>

                    <!-- Birth Date -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Data di Nascita:
                        </label>
                        <div class="relative">
                            <input id="birthDate" name="birthDate" type="text" class="py-2 px-3 pe-11 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Seleziona data" value="{% if curContact is not empty and curContact.birthDate is not empty %}{{ curContact.birthDate | date('d/m/Y') }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}
                            data-hs-datepicker='{
                                "dateFormat": "d/m/Y",
                                "locale": "it",
                                "maxDate": "today",
                                "showMonthAfterYear": false,
                                "showWeekNumbers": false,
                                "enableTime": false,
                                "altInput": true,
                                "altFormat": "d/m/Y",
                                "allowInput": true,
                                "clickOpens": true
                            }'>
                            <div class="absolute inset-y-0 end-0 flex items-center pointer-events-none pe-3">
                                <svg class="flex-shrink-0 size-4 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                                    <line x1="16" x2="16" y1="2" y2="6"/>
                                    <line x1="8" x2="8" y1="2" y2="6"/>
                                    <line x1="3" x2="21" y1="10" y2="10"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Gender -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Genere:
                        </label>
                        <div class="flex gap-4">
                            <label class="flex items-center">
                                <input type="radio" name="gender" value="MALE" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" {% if curContact is not empty and curContact.gender is not empty and curContact.gender equals 'MALE' %}checked{% endif %} {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                                <span class="text-sm text-gray-500 ms-2 dark:text-neutral-400">Maschio</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="gender" value="FEMALE" class="shrink-0 mt-0.5 border-gray-200 rounded-full text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800" {% if curContact is not empty and curContact.gender is not empty and curContact.gender equals 'FEMALE' %}checked{% endif %} {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                                <span class="text-sm text-gray-500 ms-2 dark:text-neutral-400">Femmina</span>
                            </label>
                        </div>
                    </div>

                    <!-- TIN -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Codice Fiscale:
                        </label>
                        <input type="text" id="tin" name="tin" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il codice fiscale" value="{% if curContact is not empty %}{{ curContact.tin }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>

                    <!-- Job -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Professione:
                        </label>
                        <input type="text" id="job" name="job" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci la professione" value="{% if curContact is not empty %}{{ curContact.job }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                </div>

                <!-- Company Fields (shown when contactType is COMPANY) -->
                <div id="company-fields" class="mb-4 hidden">
                    <!-- Company Name -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Ragione Sociale: <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="companyName" name="companyName" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci la ragione sociale" value="{% if curContact is not empty %}{{ curContact.companyName }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>

                    <!-- VAT Number -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Partita IVA:
                        </label>
                        <input type="text" id="vatNumber" name="vatNumber" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci la partita IVA" value="{% if curContact is not empty %}{{ curContact.vatNumber }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>

                    <!-- SDI -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Codice SDI:
                        </label>
                        <input type="text" id="sdi" name="sdi" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il codice SDI" value="{% if curContact is not empty %}{{ curContact.sdi }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Email: <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="email" name="email" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci l'email" value="{% if curContact is not empty %}{{ curContact.email }}{% endif %}" required data-msg-email="Inserisci un indirizzo email valido." {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Phone Number -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Numero di Telefono:
                    </label>
                    <input type="tel" id="phoneNumber" name="phoneNumber" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il numero di telefono" value="{% if curContact is not empty %}{{ curContact.phoneNumber }}{% endif %}" data-rule-phone="true" data-msg-phone="Inserisci un numero di telefono valido." {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Address Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-neutral-100 mb-4">Indirizzo</h3>

                    <!-- Full Address -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Indirizzo Completo:
                        </label>
                        <input type="text" id="fullAddress" name="fullAddress" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inizia a scrivere l'indirizzo..." value="{% if curContact is not empty %}{{ curContact.fullAddress }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                    </div>

                    <!-- Address and Street Number -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                Via/Piazza:
                            </label>
                            <input type="text" id="address" name="address" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci via/piazza" value="{% if curContact is not empty %}{{ curContact.address }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                Numero:
                            </label>
                            <input type="text" id="streetNumber" name="streetNumber" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="N." value="{% if curContact is not empty %}{{ curContact.streetNumber }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                        </div>
                    </div>

                    <!-- City and Postal Code -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                Città:
                            </label>
                            <select id="city" name="city" data-hs-select='{
                                "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_CITIES") }}",
                                "apiQuery": "selected={{ curContact.city }}&q",
                                "apiSearchMinLength": 1,
                                "apiDataPart": "results",
                                "apiFieldsMap": {
                                    "id": "id",
                                    "val": "id",
                                    "title": "text"
                                },
                                "apiSelectedValues": "{{ curContact.city }}",
                                "hasSearch": true,
                                "apiLoadMore": true,
                                "placeholder": "Cerca città...",
                                "toggleTag": "<button type=\"button\"></button>",
                                "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                                "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                                "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                            }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                            <option value="">Seleziona città</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                                CAP:
                            </label>
                            <input type="text" id="postalCode" name="postalCode" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Inserisci il CAP" value="{% if curContact is not empty %}{{ curContact.postalCode }}{% endif %}" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>
                        </div>
                    </div>

                    <!-- Province Code -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                            Provincia:
                        </label>
                        <select id="provinceCode" name="provinceCode" data-hs-select='{
                            "apiUrl": "{{ baseUrl }}{{ routes("BE_DATA_PROVINCES") }}",
                            "apiQuery": "selected={{ curContact.provinceCode }}&q",
                            "apiSearchMinLength": 1,
                            "apiDataPart": "results",
                            "apiFieldsMap": {
                                "id": "id",
                                "val": "id",
                                "title": "text"
                            },
                            "apiSelectedValues": "{{ curContact.provinceCode }}",
                            "hasSearch": true,
                            "apiLoadMore": true,
                            "placeholder": "Cerca provincia...",
                            "toggleTag": "<button type=\"button\"></button>",
                            "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:border-blue-500 focus:ring-blue-500 before:absolute before:inset-0 before:z-1 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600",
                            "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden overflow-y-auto dark:bg-neutral-900 dark:border-neutral-700",
                            "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                            "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span></div>"
                        }' {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona provincia</option>
                        </select>
                    </div>
                </div>

                <!-- Associated Users - Preline Advanced Select -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Utenti Associati:
                    </label>
                    <select id="userIds" name="userIds" multiple data-hs-select='{
                          "placeholder": "Seleziona Utenti...",
                          "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                          "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-3 ps-4 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                          "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                          "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                          "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http:.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                          "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                        }' class="hidden" {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="">Seleziona utenti...</option>
                        {% if allUsers is not empty %}
                            {% for user in allUsers %}
                                <option value="{{ user.id }}"
                                    {% if curContact is not empty and curContact.userIds is not empty and curContact.userIds contains user.id %}selected{% endif %}>
                                    {{ user.firstName }} {{ user.lastName }}
                                </option>
                            {% endfor %}
                        {% endif %}
                    </select>
                </div>

                <!-- Note -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Note:
                    </label>
                    <textarea id="note" name="note" rows="4" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Inserisci eventuali note..." maxlength="1000" data-msg-maxlength="Le note non possono superare i 1000 caratteri." {% if not user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}readonly{% endif %}>{% if curContact is not empty %}{{ curContact.note }}{% endif %}</textarea>
                </div>

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 py-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curContact is empty %}
                <!-- New Contact - Show Save Button -->
                {% if user.hasPermission('CONTACT_MANAGEMENT', 'create') %}
                <button type="submit" form="contact-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Contatto
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare contatti
                </div>
                {% endif %}
            {% else %}
                <!-- Edit Contact - Show Update and Delete Buttons -->
                {% if user.hasPermission('CONTACT_MANAGEMENT', 'delete') %}
                <button type="button" data-contactid="{{ curContact.id }}" id="delete-contact-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('CONTACT_MANAGEMENT', 'edit') %}
                <button type="submit" form="contact-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Contatto
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare contatti
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>