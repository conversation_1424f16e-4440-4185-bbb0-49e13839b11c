<!-- User Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_USER', '{{ routes("BE_USER") }}');
    addRoute('BE_USER_SAVE', '{{ routes("BE_USER_SAVE") }}');
    addRoute('BE_USER_OPERATE', '{{ routes("BE_USER_OPERATE") }}');
    addVariables('imageId', '{{ curUser.imageId }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_USER_SAVE') %}
            {% if curUser.id is not empty %}                
            {% set postUrl = routes('BE_USER_SAVE') + '?userId=' + curUser.id %}
            {% endif %}

            <form id="user-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">
                <!-- Profile Image -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Immagine profilo:
                    </label>
                    <div class="space-y-2 flex flex-col justify-center items-center text-center">
                        <input id="logo-offcanvas" name="imageId" type="file" class="filepond filepond-avatar">
                        <p class="text-xs text-gray-500 dark:text-neutral-400">Formato immagine .jpg, .png o .svg.</p>
                    </div>
                </div>    

                <!-- Name -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Nome: <span class="text-red-500">*</span>
                    </label>
                    <input name="name" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Nome" value="{{ curUser.name }}" required {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Lastname -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Cognome: <span class="text-red-500">*</span>
                    </label>
                    <input name="lastname" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Cognome" value="{{ curUser.lastname }}" required {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Phone -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Telefono:
                    </label>
                    <input name="phoneNumber" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" value="{{ curUser.phoneNumber }}" {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Informations -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Informazioni:
                    </label>
                    <textarea name="informations" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" rows="3" {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}readonly{% endif %}>{{ curUser.informations }}</textarea>
                </div>

                <!-- Email -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Email di accesso: <span class="text-red-500">*</span>
                    </label>
                    <input name="username" type="email" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" value="{{ curUser.username }}" {{ disabled }} required {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Password -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Password: <span class="text-red-500">*</span>
                    </label>
                    <input name="password" type="password" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" value="{{ curUser.password }}" required {{ disabled }} {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}readonly{% endif %}>
                </div>

                <!-- Profile Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Tipologia Profilo: <span class="text-red-500">*</span>
                    </label>
                    <select name="profileType" class="py-2 px-3 pe-9 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:ring-neutral-600" data-hs-select='{
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200\" data-title></span></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 max-h-72 pb-1 px-1 space-y-0.5 z-20 w-full bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800"
                    }' {% if not user.hasPermission('USER_MANAGEMENT', 'edit') %}disabled{% endif %}>
                        <option value="unconfirmed" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'unconfirmed') ? 'selected' : '' }}>Non confermato</option>
                        <option value="customer" {{ (curUser is not empty and curUser.profileType is not empty and curUser.profileType equals 'customer') ? 'selected' : '' }}>Confermato</option>
                    </select>
                </div>

                <!-- Permissions Management Link -->
                {% if curUser is not empty and curUser.id is not empty and user.hasPermission('PERMISSION_MANAGEMENT', 'view') %}
                <div class="mb-6">
                    <div class="border-t border-gray-200 dark:border-neutral-700 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Gestione Permessi</h3>
                        <p class="text-sm text-gray-600 dark:text-neutral-400 mb-4">
                            Per gestire i permessi di questo utente, utilizza la pagina dedicata alla gestione permessi.
                        </p>
                        <a href="{{ routes('BE_USER_PERMISSIONS_MANAGER') }}" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                <path d="m9 12 2 2 4-4"></path>
                            </svg>
                            Gestisci Permessi Utente
                        </a>
                    </div>
                </div>
                {% endif %}

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 py-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curUser is empty %}
                <!-- New User - Show Save Button -->
                {% if user.hasPermission('USER_MANAGEMENT', 'create') %}
                <button type="submit" form="user-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Salva Utente
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per creare utenti
                </div>
                {% endif %}
            {% else %}
                <!-- Edit User - Show Update and Delete Buttons -->
                {% if user.hasPermission('USER_MANAGEMENT', 'delete') %}
                <button type="button" data-userid="{{ curUser.id }}" id="delete-user-btn" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-red-600 hover:bg-red-50 focus:outline-none focus:bg-red-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-red-500 dark:hover:bg-red-800/30 dark:focus:bg-red-800/30">
                    <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 6h18"/>
                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                        <line x1="10" x2="10" y1="11" y2="17"/>
                        <line x1="14" x2="14" y1="11" y2="17"/>
                    </svg>
                    Elimina
                </button>
                {% endif %}
                {% if user.hasPermission('USER_MANAGEMENT', 'edit') %}
                <button type="submit" form="user-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                    Modifica Utente
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per modificare utenti
                </div>
                {% endif %}
            {% endif %}
        </div>
    </div>
</div>
