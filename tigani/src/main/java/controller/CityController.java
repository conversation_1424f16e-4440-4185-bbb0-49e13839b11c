package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class CityController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CityController.class.getName());

    public static TemplateViewRoute be_city_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CITY_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_CITY_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_city = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CITY_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("cityId"));
        if (oid != null) {
            City loadedCity = BaseDao.getDocumentById(oid, City.class);
            attributes.put("curCity", loadedCity);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                City loadedCity = BaseDao.getDocumentByParentId(parentId, City.class);
                if (loadedCity != null) {
                    attributes.put("curCity", loadedCity);
                }
            }
        }

        return Core.render(Pages.BE_CITY, attributes, request);
    };

    public static TemplateViewRoute be_city_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CITY_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("cityId"));
        if (oid != null) {
            City loadedCity = BaseDao.getDocumentById(oid, City.class);
            attributes.put("curCity", loadedCity);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                City loadedCity = BaseDao.getDocumentByParentId(parentId, City.class);
                if (loadedCity != null) {
                    attributes.put("curCity", loadedCity);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_CITY_FORM, attributes, request);
    };

    public static Route be_city_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CITY_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<City> loadedCities;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedCities = BaseDao.getDocumentsByFilters(City.class, queryOptions, loadArchived);
        } else {
            loadedCities = BaseDao.getDocumentsByFilters(City.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedCities.isEmpty()) {
            for (City tmpCity : loadedCities) {
                List<String> row = new ArrayList<>();
                row.add(tmpCity.getId().toString()); // ID for row identification

                // Name con link
                String nameLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' cityId='" +
                    tmpCity.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpCity.getName(), "N.D.") + "</a>";
                row.add(nameLink);

                row.add(StringUtils.defaultIfBlank(tmpCity.getCodiceIstat(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpCity.getCap(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpCity.getProvinceCode(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpCity.getProvince(), "N.D."));
                row.add(StringUtils.defaultIfBlank(tmpCity.getRegion(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpCity.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpCity.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_city_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("cityId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CITY_MANAGEMENT.getCode(), requiredPermission);

        City newCity;
        if (oid != null) {
            newCity = BaseDao.getDocumentById(oid, City.class);
            RequestUtils.mergeFromParams(params, newCity);
        } else {
            newCity = RequestUtils.createFromParams(params, City.class);
        }

        if (newCity != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newCity);
                newCity.setId(oid);

                BaseDao.insertLog(user, newCity, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newCity);
                BaseDao.insertLog(user, newCity, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_city_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.CITY_MANAGEMENT.getCode(), requiredPermission);

        String cityIds = params.get("cityIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(cityIds)) {
            String[] ids = cityIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    City tmpCity = BaseDao.getDocumentById(oid, City.class);
                    if (tmpCity != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpCity);
                                BaseDao.insertLog(user, tmpCity, LogType.DELETE);
                                break;
                            case "archive":
                                tmpCity.setArchived(true);
                                BaseDao.updateDocument(tmpCity);
                                BaseDao.insertLog(user, tmpCity, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpCity.setArchived(false);
                                BaseDao.updateDocument(tmpCity);
                                BaseDao.insertLog(user, tmpCity, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
