/**
 * Example usage of the parameterized initAutocomplete() function
 * This file demonstrates how to use the function with different field IDs
 */

// Example 1: Using default field IDs (backward compatibility)
function initAutocompleteDefault() {
    // This will use the default field IDs:
    // inputField: 'fullAddress'
    // fields: { city: 'city', address: 'address', streetNumber: 'streetNumber', etc. }
    initAutocomplete();
}

// Example 2: Using custom field IDs for a second address form
function initAutocompleteSecondary() {
    initAutocomplete({
        inputField: 'fullAddress2',
        fields: {
            city: 'city2',
            address: 'address2',
            streetNumber: 'streetNumber2',
            postalCode: 'postalCode2',
            countryCode: 'countryCode2',
            provinceCode: 'provinceCode2'
        }
    });
}

// Example 3: Using custom field IDs for a billing address form
function initAutocompleteBilling() {
    initAutocomplete({
        inputField: 'billingFullAddress',
        fields: {
            city: 'billingCity',
            address: 'billingAddress',
            streetNumber: 'billingStreetNumber',
            postalCode: 'billingPostalCode',
            countryCode: 'billingCountryCode',
            provinceCode: 'billingProvinceCode'
        }
    });
}

// Example 4: Partial customization (only some fields different)
function initAutocompletePartial() {
    initAutocomplete({
        inputField: 'shippingAddress',
        fields: {
            city: 'shippingCity',
            address: 'shippingAddress',
            // Other fields will use defaults: streetNumber, postalCode, countryCode, provinceCode
        }
    });
}

/**
 * HTML structure examples for different scenarios:
 * 
 * Default usage:
 * <input id="fullAddress" type="text" placeholder="Enter address">
 * <input id="city" type="text">
 * <input id="address" type="text">
 * <input id="streetNumber" type="text">
 * <input id="postalCode" type="text">
 * <select id="countryCode"></select>
 * <select id="provinceCode"></select>
 * 
 * Secondary address form:
 * <input id="fullAddress2" type="text" placeholder="Enter second address">
 * <input id="city2" type="text">
 * <input id="address2" type="text">
 * <input id="streetNumber2" type="text">
 * <input id="postalCode2" type="text">
 * <select id="countryCode2"></select>
 * <select id="provinceCode2"></select>
 * 
 * Billing address form:
 * <input id="billingFullAddress" type="text" placeholder="Enter billing address">
 * <input id="billingCity" type="text">
 * <input id="billingAddress" type="text">
 * <input id="billingStreetNumber" type="text">
 * <input id="billingPostalCode" type="text">
 * <select id="billingCountryCode"></select>
 * <select id="billingProvinceCode"></select>
 */

/**
 * Test function to verify backward compatibility
 * This function tests that calling initAutocomplete() without parameters
 * still works with the default field IDs
 */
function testBackwardCompatibility() {
    console.log('Testing backward compatibility...');

    // Test 1: Call without parameters (should use defaults)
    try {
        initAutocomplete();
        console.log('✓ initAutocomplete() called successfully without parameters');
        console.log('✓ Should use default inputField: fullAddress');
        console.log('✓ Should use default field IDs: city, address, streetNumber, etc.');
    } catch (error) {
        console.error('✗ Error calling initAutocomplete() without parameters:', error);
    }

    // Test 2: Call with null (should use defaults)
    try {
        initAutocomplete(null);
        console.log('✓ initAutocomplete(null) called successfully');
    } catch (error) {
        console.error('✗ Error calling initAutocomplete(null):', error);
    }

    // Test 3: Call with undefined (should use defaults)
    try {
        initAutocomplete(undefined);
        console.log('✓ initAutocomplete(undefined) called successfully');
    } catch (error) {
        console.error('✗ Error calling initAutocomplete(undefined):', error);
    }

    console.log('Backward compatibility test completed.');
}
