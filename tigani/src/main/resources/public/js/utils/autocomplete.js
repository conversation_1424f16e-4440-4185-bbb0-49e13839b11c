// Global variable to store current autocomplete options
var currentAutocompleteOptions = null;

// async registering autocomplete address
function initAutocomplete(options) {
    // Default field IDs for backward compatibility
    var defaultOptions = {
        inputField: 'fullAddress',
        fields: {
            city: 'city',
            address: 'address',
            streetNumber: 'streetNumber',
            postalCode: 'postalCode',
            countryCode: 'countryCode',
            provinceCode: 'provinceCode'
        }
    };

    // Merge provided options with defaults
    currentAutocompleteOptions = options ? {
        inputField: options.inputField || defaultOptions.inputField,
        fields: Object.assign({}, defaultOptions.fields, options.fields || {})
    } : defaultOptions;

    var input = $('#' + currentAutocompleteOptions.inputField)[0];
    var googleMapsOptions = {
        offset: 2
    };
    autocomplete = new google.maps.places.Autocomplete(input, googleMapsOptions);
    autocomplete.addListener('place_changed', selectAddress);

    // hacking google logo
    setTimeout(function () {
        $('.pac-container').removeClass('pac-logo');
    }, 1000);
}

/**
 * Fetch entity data using the new BE_DATA_ENTITY route
 * @param {string} entityType - Type of entity ('city', 'province', 'country')
 * @param {string} value - Search value
 * @param {string} provinceCode - Optional province code for city searches
 * @param {string} countryCode - Optional country code for city searches
 * @returns {Promise} Promise that resolves with entity data or null
 */
function fetchEntityData(entityType, value, provinceCode = null, countryCode = null) {
    return new Promise((resolve, reject) => {
        if (!value || !entityType) {
            resolve(null);
            return;
        }

        var params = {
            entityType: entityType,
            value: value
        };

        // Add optional parameters for city searches
        if (provinceCode) {
            params.provinceCode = provinceCode;
        }
        if (countryCode) {
            params.countryCode = countryCode;
        }

        $.ajax({
            url: appRoutes.get("BE_DATA_ENTITY"),
            type: 'GET',
            data: params,
            dataType: 'json',
            success: function(response) {
                resolve(response);
            },
            error: function(xhr, status, error) {
                // Log error but don't reject - just return null for no match
                console.log('Entity lookup failed for ' + entityType + ' "' + value + '": ' + error);
                resolve(null);
            }
        });
    });
}

/**
 * Add option to HSSelect instance
 * @param {string} selectId - ID of the select element
 * @param {string} title - Display text for the option
 * @param {string} val - Value for the option
 */
function addSelectOption(selectId, title, val) {
    try {
        const selectElement = document.getElementById(selectId);
        if (selectElement) {
            const selectInstance = HSSelect.getInstance(selectElement, false);
            if (selectInstance) {
                // clear selected value if any
                if ($('#' + selectId).val()) {
                    selectInstance.setValue('');
                }

                selectInstance.addOption({
                    title: title,
                    val: val,
                    selected: true
                });
            }
        }
    } catch (error) {
        console.error('Error adding option to select ' + selectId + ':', error);
    }
}

function selectAddress() {
    // get city
    var city = '';
    var address = '';
    var number = '';
    var postalCode = '';
    var countryCode = '';
    var provinceCode = '';
    var venue = '';
    var place = autocomplete.getPlace();
    if (place !== null) {
        if (typeof place.address_components !== 'undefined') {
            for (var i = 0; i < place.address_components.length; i++) {
                var type = place.address_components[i].types[0];
                if (type === 'administrative_area_level_3') {
                    city = place.address_components[i]['long_name'];
                }
                if (type === 'route') {
                    address = place.address_components[i]['long_name'];
                }
                if (type === 'street_number') {
                    number = place.address_components[i]['long_name'];
                }
                if (type === 'postal_code') {
                    postalCode = place.address_components[i]['long_name'];
                }
                if (type === 'administrative_area_level_2') {
                    provinceCode = place.address_components[i]['short_name'];
                }
                if (type === 'country') {
                    countryCode = place.address_components[i]['short_name'];
                }
                //                  type for venue
                //                "point_of_interest"
                //                "establishment"
                if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                    venue = place.name;
                }
            }
            if (address !== '') {
                if (number !== '') {
                    address += ' ' + number;
                }
            }
        }
    }

    console.log('city: ' + city + ', address: ' + address + ', number: ' + number + ', postalCode: ' + postalCode + ', provinceCode: ' + provinceCode + ', countryCode: ' + countryCode + ', venue: ' + venue);

    // Set basic address fields using configurable field IDs
    if (currentAutocompleteOptions && currentAutocompleteOptions.fields) {
        $('#' + currentAutocompleteOptions.fields.city).val(city);
        $('#' + currentAutocompleteOptions.fields.address).val(address);
        $('#' + currentAutocompleteOptions.fields.streetNumber).val(number);
        $('#' + currentAutocompleteOptions.fields.postalCode).val(postalCode);
    }

    // Handle entity lookups and HSSelect integration
    handleEntityLookups(city, provinceCode, countryCode);
}

/**
 * Handle entity lookups and HSSelect integration for autocomplete results
 * @param {string} city - City name from Google Places
 * @param {string} provinceCode - Province code from Google Places
 * @param {string} countryCode - Country code from Google Places
 */
async function handleEntityLookups(city, provinceCode, countryCode) {
    try {
        // Handle country lookup and HSSelect integration
        if (countryCode && currentAutocompleteOptions && currentAutocompleteOptions.fields) {
            const countryEntity = await fetchEntityData('country', countryCode);
            if (countryEntity) {
                // Add option to country select using HSSelect
                addSelectOption(currentAutocompleteOptions.fields.countryCode, countryEntity.description, countryEntity.code);
            } else {
                // Fallback to basic value setting if entity not found
                $('#' + currentAutocompleteOptions.fields.countryCode).val(countryCode).change();
            }
        }

        // Handle province lookup and HSSelect integration
        if (provinceCode && currentAutocompleteOptions && currentAutocompleteOptions.fields) {
            const provinceEntity = await fetchEntityData('province', provinceCode);
            if (provinceEntity) {
                // Add option to province select using HSSelect
                addSelectOption(currentAutocompleteOptions.fields.provinceCode, provinceEntity.description, provinceEntity.code);
            } else {
                // Fallback to basic value setting if entity not found
                $('#' + currentAutocompleteOptions.fields.provinceCode).val(provinceCode).change();
            }
        }

        // Handle city lookup and HSSelect integration
        if (city && currentAutocompleteOptions && currentAutocompleteOptions.fields) {
            const cityEntity = await fetchEntityData('city', city, provinceCode, countryCode);
            if (cityEntity) {
                // For city, we might want to show "CityName (Province)" format
                const displayText = cityEntity.province ?
                    cityEntity.name + ' (' + cityEntity.province + ')' :
                    cityEntity.name;

                // Add option to city select using HSSelect (if city select exists)
                addSelectOption(currentAutocompleteOptions.fields.city, displayText, cityEntity.name);
            }
            // Note: City field might be a text input, so we keep the original value setting as well
        }

    } catch (error) {
        console.error('Error in entity lookups:', error);
        // Fallback to original behavior using configurable field IDs
        if (currentAutocompleteOptions && currentAutocompleteOptions.fields) {
            $('#' + currentAutocompleteOptions.fields.provinceCode).val(provinceCode).change();
            $('#' + currentAutocompleteOptions.fields.countryCode).val(countryCode).change();
        }
    }
}