const CountryForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitCountry();
        _componentInputFormatting();
    };

    // Validation config using centralized factory
    const _componentValidate = function () {
        // Custom validation rules for country
        $.validator.addMethod('countryCode', function (value) {
            return /^[A-Z0-9]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole e numeri.');

        // Custom validation options for country form
        const customOptions = {};

        // Use centralized form validation factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', null, customOptions);
    };

    // Maxlength using centralized utility
    const _componentMaxlength = function () {
        // Custom maxlength configuration for country form
        const customConfig = {
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        };

        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        $('.maxlength').maxlength(customConfig);
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-country-btn',
            permissionCheck: 'COUNTRY_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-countryid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo paese? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(countryId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('countryIds', countryId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_COUNTRY_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.countriesDataTable && window.countriesDataTable.dataTable) {
                            window.countriesDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Paese eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Paese eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during country deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks using centralized utility
    const _componentPermissionChecks = function () {
        // Use centralized permission-based visibility
        TiganiLibs.ActionHandlerFactory.initPermissionBasedVisibility({
            '.btn-delete': {code: 'COUNTRY_MANAGEMENT', type: 'delete'}
        });

        // Custom permission checks for form fields
        if (typeof hasPermission === 'function') {
            // Check if user has edit permissions for form fields
            if (!hasPermission('COUNTRY_MANAGEMENT', 'edit')) {
                // Disable all form inputs
                $('#country-edit input, #country-edit textarea, #country-edit select').prop('readonly', true);
                $('#country-edit select').prop('disabled', true);
                $('#country-edit-offcanvas input, #country-edit-offcanvas textarea, #country-edit-offcanvas select').prop('readonly', true);
                $('#country-edit-offcanvas select').prop('disabled', true);

                // Add visual indication
                $('#country-edit, #country-edit-offcanvas').addClass('opacity-75');
            }

            // Check create permissions for new country forms
            const isNewCountry = !$('#country-edit input[name="id"]').val() && !$('#country-edit-offcanvas input[name="id"]').val();
            if (isNewCountry && !hasPermission('COUNTRY_MANAGEMENT', 'create')) {
                // Disable all form inputs
                $('#country-edit input, #country-edit textarea, #country-edit select').prop('disabled', true);
                $('#country-edit-offcanvas input, #country-edit-offcanvas textarea, #country-edit-offcanvas select').prop('disabled', true);

                // Add visual indication
                $('#country-edit, #country-edit-offcanvas').addClass('opacity-50');
            }
        }
    }

    const _componentSubmitCountry = function () {
        var idForm = "country-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewCountry = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewCountry) {
                    if (!hasPermission('COUNTRY_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare paesi.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('COUNTRY_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare paesi.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.countriesDataTable && window.countriesDataTable.dataTable) {
                                window.countriesDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Paese salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Paese salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during country save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="code"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Codice Belfiore field - uppercase transformation
        $('input[name="codiceBelfiore"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Description field - trim whitespace
        $('input[name="description"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    CountryForm.init();
});


