var oldImageBase64;

const DealerForm = function () {
    // Initialization of components
    const init = function () {
        _componentFilePond();
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitDealer();
    };

    // FilePond using centralized factory
    const _componentFilePond = function () {
        TiganiLibs.UIComponentFactory.initFilePond('input[type="file"]', {
            onaddfile: function(error, file) {
                if (!error) {
                    oldImageBase64 = file.getFileEncodeBase64String();
                }
            }
        });
    };

    // Validation using centralized factory
    const _componentValidate = function () {
        // Custom validation options for dealer form
        const customOptions = {};

        // Use centralized form validation factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', null, customOptions);

        // Custom validation methods for dealer form
        $.validator.addMethod('vatNumber', function (value, element) {
            return this.optional(element) || /^[0-9]{11}$/.test(value);
        }, 'Partita IVA deve contenere esattamente 11 cifre.');

        $.validator.addMethod('fiscalCode', function (value, element) {
            return this.optional(element) || /^[A-Z0-9]{16}$/.test(value.toUpperCase());
        }, 'Codice fiscale deve contenere esattamente 16 caratteri alfanumerici.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-dealer-btn',
            permissionCheck: 'DEALER_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-dealerid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo rivenditore? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(dealerId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('dealerIds', dealerId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_DEALER_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // Check if we're in an offcanvas (for future compatibility)
                        const isOffcanvas = buttonElement.closest('.hs-overlay').length > 0;

                        // Show success toast and redirect after delay
                        if (typeof showToast === 'function') {
                            showToast('Rivenditore eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Rivenditore eliminato correttamente');
                        }

                        if (isOffcanvas) {
                            $.unblockUI();
                        } else {
                            setTimeout(() => {
                                window.location.href = appRoutes.get('BE_DEALER_COLLECTION');
                            }, 1000);
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during dealer deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('DEALER_MANAGEMENT', 'edit')) {
            // Disable all form inputs for both page and offcanvas forms
            $('#dealer-edit input, #dealer-edit textarea, #dealer-edit select').prop('readonly', true);
            $('#dealer-edit select').prop('disabled', true);
            $('#dealer-edit-offcanvas input, #dealer-edit-offcanvas textarea, #dealer-edit-offcanvas select').prop('readonly', true);
            $('#dealer-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#dealer-edit, #dealer-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new dealer forms
        const isNewDealerPage = !$('#dealer-edit input[name="id"]').val();
        const isNewDealerOffcanvas = !$('#dealer-edit-offcanvas input[name="id"]').val();
        const isNewDealer = isNewDealerPage || isNewDealerOffcanvas;

        if (isNewDealer && !hasPermission('DEALER_MANAGEMENT', 'create')) {
            // Disable all form inputs for both page and offcanvas forms
            $('#dealer-edit input, #dealer-edit textarea, #dealer-edit select').prop('disabled', true);
            $('#dealer-edit-offcanvas input, #dealer-edit-offcanvas textarea, #dealer-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#dealer-edit, #dealer-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitDealer = function () {
        // Form submission handling - works for both offcanvas and page forms
        $('#dealer-edit-offcanvas, #dealer-edit').submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewDealer = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewDealer) {
                    if (!hasPermission('DEALER_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare rivenditori.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('DEALER_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare rivenditori.', 'error');
                        return;
                    }
                }

                // Handle FilePond file if present
                try {
                    if (pond.getFiles().length > 0) {
                        // Ottieni la stringa base64 del file croppato
                        const fileToInsert = pond.getFiles()[0];
                        const base64String = fileToInsert.getFileEncodeBase64String();

                        if (oldImageBase64 === base64String) {
                            // add field to specify that image is the same and should not be updated
                            formData.append('sameImage', true);
                        } else {
                            const mimeType = fileToInsert.fileType;
                            const blob = base64ToBlob(base64String, mimeType);
                            const fileName = fileToInsert.filename;
                            const file = new File([blob], fileName, {type: mimeType});

                            // Aggiungi il file croppato al FormData
                            formData.append('file', file);
                        }
                    }
                } catch (fileError) {
                    console.warn('Error processing file upload:', fileError);
                    // Continue without file if there's an error
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        const $form = $(this);

                        try {
                            // Check if we're in an offcanvas (for future compatibility)
                            const isOffcanvas = $form.closest('.hs-overlay').length > 0;

                            if (isOffcanvas) {
                                $.unblockUI();
                                // Close offcanvas and reload table
                                const overlayElement = $form.closest(".hs-overlay.opened")[0];
                                if (overlayElement) {
                                    const overlay = HSOverlay.getInstance(overlayElement, true);
                                    if (overlay) {
                                        overlay.element.close();
                                    }
                                }

                                // Reload table with error handling
                                if (window.dealersDataTable && window.dealersDataTable.dataTable) {
                                    window.dealersDataTable.dataTable.ajax.reload(null, false); // Keep current page
                                }

                                // Show success message
                                showToast('Rivenditore salvato correttamente', 'success');
                            } else {
                                showToast('Rivenditore salvato correttamente', 'success');
                                setTimeout(() => {
                                    window.location.href = appRoutes.get('BE_DEALER_COLLECTION');
                                }, 1000);
                            }
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            // Show error toast and redirect after delay
                            showToast('Si è verificato un errore', 'error');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during dealer save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Funzione ausiliaria per convertire base64 in Blob
function base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
    }

    const byteArray = new Uint8Array(byteArrays);
    return new Blob([byteArray], {type: mimeType});
}
