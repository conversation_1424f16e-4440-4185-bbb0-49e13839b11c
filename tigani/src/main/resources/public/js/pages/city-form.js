const CityForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitCity();
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for city
        $.validator.addMethod('cityCode', function (value) {
            return /^[A-Z0-9]+$/.test(value);
        }, 'Il codice può contenere solo lettere maiuscole e numeri.');

        // Define custom rules for this form
        const customRules = {
            code: {
                required: true,
                cityCode: true,
                maxlength: 10
            },
            name: {
                required: true,
                maxlength: 100
            },
            provinceId: {
                required: true
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-city-btn',
            permissionCheck: 'CITY_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-cityid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questa città? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(cityId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('cityIds', cityId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_CITY_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.citiesDataTable && window.citiesDataTable.dataTable) {
                            window.citiesDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Città eliminata correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Città eliminata correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during city deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('CITY_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#city-edit input, #city-edit textarea, #city-edit select').prop('readonly', true);
            $('#city-edit select').prop('disabled', true);
            $('#city-edit-offcanvas input, #city-edit-offcanvas textarea, #city-edit-offcanvas select').prop('readonly', true);
            $('#city-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#city-edit, #city-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new city forms
        const isNewCity = !$('#city-edit input[name="id"]').val() && !$('#city-edit-offcanvas input[name="id"]').val();
        if (isNewCity && !hasPermission('CITY_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#city-edit input, #city-edit textarea, #city-edit select').prop('disabled', true);
            $('#city-edit-offcanvas input, #city-edit-offcanvas textarea, #city-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#city-edit, #city-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitCity = function () {
        var idForm = "city-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewCity = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewCity) {
                    if (!hasPermission('CITY_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare città.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('CITY_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare città.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.citiesDataTable && window.citiesDataTable.dataTable) {
                                window.citiesDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Città salvata correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Città salvata, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during city save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Province code field - uppercase transformation
        $('input[name="provinceCode"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Country code field - uppercase transformation
        $('input[name="countryCode"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Codice Belfiore field - uppercase transformation
        $('input[name="codiceBelfiore"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Name field - trim whitespace
        $('input[name="name"]').on('blur', function() {
            this.value = this.value.trim();
        });

        // Province and region fields - trim whitespace
        $('input[name="province"], input[name="region"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    CityForm.init();
});
